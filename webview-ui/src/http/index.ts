import {
  fetchEventSource,
  FetchEventSourceInit,
} from "@fortaine/fetch-event-source";
import {
  CodeSearchCheckoutRequest,
  CodeSearchGeneratePromptRequest,
  ComposerPromptBody,
  GetCodeSearchCheckoutResponse,
  GetCodeSearchPromptResponse,
  HttpClientResponse,
  LocalCodeSearchGeneratePromptRequest,
  PlatformConfigResponse,
  WidgetConfig,
} from "@/http/interface";
import { logger } from "@/utils/logger";
import { Doc, Model } from "@shared/types/business";
import { DEFAULT_PROXY_URL } from "@/constant";
import baseInfoManager from "@/utils/baseInfo";
import { CustomPromptData } from "shared/lib/CustomVariable";
import statuses from "statuses";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { firstValueFrom } from "rxjs";

interface HttpClientOptions {
  config?: FetchEventSourceInit;
  requestInterceptors?: ((param: any) => any)[];
  responseInterceptors?: ((param: any) => any)[];
}

export class HttpError extends Error {
  constructor(public code: number, public message: string, public response?: Response) {
    super(message || statuses(code));
  }
}

class HttpClient {
  private _baseUrl = window.proxyUrl || DEFAULT_PROXY_URL;
  private _config: RequestInit = {};
  private requestInterceptors: ((param: any) => any)[] = [];
  private responseInterceptors: ((param: any) => any)[] = [];
  constructor(options?: HttpClientOptions) {
    this._config = options?.config || {};
    this.requestInterceptors = options?.requestInterceptors || [];
    this.responseInterceptors = options?.responseInterceptors || [];
  }

  async request<R>(url: string, requestInit?: RequestInit) {
    const response = await this.rawRequest(url, requestInit);

    let responseData = (await response.json()) as R;

    this.responseInterceptors.forEach((interceptor) => {
      responseData = interceptor(responseData);
    });

    logger.debug("api response after interceptor", "http-client", {
      value: responseData,
    });

    return responseData;
  }

  async rawRequest(url: string, requestInit?: RequestInit) {
    let config = this.mergeConfig(this._config, requestInit);
    config.headers = {
      "Content-Type": "application/json",
      ...config.headers,
    };
    config.method = config.method || "GET";
    let response;
    const u = new URL(url, this._baseUrl);

    logger.debug(`api request url: ${u.toString()}`, "http-client", {
      value: {
        url: u.toString(),
        config,
      },
    });
    this.requestInterceptors.forEach((interceptor) => {
      config = interceptor(config);
    });

    logger.debug("api request after interceptor", "http-client", {
      value: {
        url: u.toString(),
        config,
      },
    });
    try {
      response = await fetch(u, config);
    }
    catch (error: any) {
      if (error.type === "aborted") {
        throw "user abort";
      }
      logger.error("api request error", "http-client", {
        value: {
          url: u.toString(),
          config,
        },
        err: error,
      });
      throw new Error(error.toString());
    }

    if (!response.ok) {
      logger.error("api request error", "http-client", {
        value: {
          url: u.toString(),
          config,
          status: response.status,
          statusText: response.statusText,
        },
        reason: "an error occurred",
      });
      throw new HttpError(response.status, response.statusText, response);
    }
    logger.info(
      `api request success: ${config.method} ${u.pathname} ${response.status}`,
      "http-client",
      {
        value: {
          url: u.toString(),
          config,
          response,
        },
      },
    );
    return response;
  }

  async get<T, R = HttpClientResponse<T>>(
    url: string,
    params: Record<string, string> = {},
    config: RequestInit = {},
  ) {
    const p = this.formatUrl(url, params);
    config.method = "GET";
    return this.request<R>(p, config);
  }

  async post<T, R = HttpClientResponse<T>, D extends BodyInit = any>(
    url: string,
    body?: D,
    config: RequestInit = {},
  ) {
    config.method = "POST";
    config.body = body;
    return this.request<R>(url, config);
  }

  rawFetchEventSource(url: string, options: FetchEventSourceInit) {
    let u = null;
    if (typeof url === "string") {
      u = new URL(url, this._baseUrl);
    }
    logger.debug("fetch event source", "http-client", {
      value: options.body,
    });
    return fetchEventSource(u ? u.toString() : url, options);
  }

  private mergeConfig(
    originConfig: RequestInit = {},
    config: RequestInit = {},
  ) {
    return { ...originConfig, ...config };
  }

  private formatUrl(url: string, params: Record<string, string>) {
    let isUrl = true;
    const urlObj = new URL(url, "http://example.com");

    try {
      new URL(url);
    }
    catch (error) {
      isUrl = false;
    }

    // 获取现有的搜索参数
    const searchParams = urlObj.searchParams;

    // 遍历新的参数对象，并添加到搜索参数中
    for (const [key, value] of Object.entries(params)) {
      searchParams.append(key, value);
    }

    if (!isUrl) {
      return `${urlObj.pathname}${urlObj.search}`;
    }

    return urlObj.toString();
  }
}

export class Api extends HttpClient {
  // 添加 usernamePromise 成员
  private usernamePromise: Promise<string | undefined>;

  constructor(config?: HttpClientOptions) {
    super(config);
    // 在构造函数中初始化 usernamePromise
    this.usernamePromise = this.initUsername();
  }

  // 添加初始化用户名的方法
  private async initUsername(): Promise<string | undefined> {
    try {
      const userInfo = await firstValueFrom(kwaiPilotBridgeAPI.observableAPI.userInfo());
      return userInfo?.name;
    }
    catch (error) {
      logger.error("Failed to get username", "http-client", { err: error });
      return undefined;
    }
  }

  // 添加获取用户名的方法
  private async getUsername(): Promise<string | undefined> {
    return this.usernamePromise;
  }

  async getPlatformConfig() {
    const { data } = await this.get<PlatformConfigResponse>(
      "/eapi/kwaipilot/plugin/config",
    );
    return data;
  }

  async getWidget() {
    const key = "kinsight.kwaipilot.activityWidget";
    const version = baseInfoManager.pluginVersion;
    const username = await this.getUsername();
    const { data } = await this.get<WidgetConfig>(
      `/eapi/kwaipilot/plugin/v2/config?key=${key}&pluginVersion=${version}&username=${username}`,
    );
    return data;
  }

  async getCodeSearchPrompt(body: CodeSearchGeneratePromptRequest) {
    const { data } = await this.post<GetCodeSearchPromptResponse>(
      "/eapi/kwaipilot/plugin/code/search/generate_prompt",
      JSON.stringify(body),
    );

    return data;
  }

  async getLocalCodeSearchPrompt(body: LocalCodeSearchGeneratePromptRequest) {
    const { data } = await this.post<string>(
      "/eapi/kwaipilot/plugin/code/search/local-codesearch-prompt",
      JSON.stringify(body),
    );
    return data;
  }

  async getCodeSearchCheckout(
    body: CodeSearchCheckoutRequest,
    signal: AbortSignal,
  ) {
    const { data } = await this.post<GetCodeSearchCheckoutResponse>(
      "/eapi/kwaipilot/plugin/code/search/checkout",
      JSON.stringify(body),
      { signal },
    );

    return data;
  }

  async getModelList() {
    const username = await this.getUsername();
    const { data } = await this.get<Model[]>(
      `/eapi/kwaipilot/plugin/chat_model?username=${username}`,
    );
    return data;
  }

  async getDocList() {
    const { data } = await this.get<Doc[]>(
      "/eapi/kwaipilot/plugin/kwai_knowledge_repo_list",
    );

    return data;
  }

  /** 根据用户问题生成对话标题 */
  async summaryConversation(question: string) {
    const username = await this.getUsername();
    const body = {
      platform: baseInfoManager.ide,
      userQuestion: question,
      username: username ?? "",
    };
    return await this.rawRequest(
      "/eapi/kwaipilot/plugin/tool/summaryConversation",
      {
        body: JSON.stringify(body),
        method: "POST",
      },
    );
  }

  /** 获取推荐问题 */
  async getSuggestQuestion(params: { question: string; answer: string }) {
    const username = await this.getUsername();
    const body = {
      ...params,
      username: username ?? "",
      platform: baseInfoManager.ide,
    };
    const { data } = await this.post<string[]>(
      "/eapi/kwaipilot/plugin/tool/get_suggest_question",
      JSON.stringify(body),
    );
    return data;
  }

  /** 获取用户意图 */
  async getUserIntention(
    params: {
      sessionId: string;
      chatId: string;
      question: string;
    },
    signal: AbortSignal,
  ) {
    const username = await this.getUsername();
    const body = {
      ...params,
      username: username ?? "",
      platform: baseInfoManager.ide,
      pluginVersion: baseInfoManager.pluginVersion,
    };
    const { data } = await this.post<string>(
      "/eapi/kwaipilot/chat/generate/intent-recognition",
      JSON.stringify(body),
      { signal },
    );
    return data;
  }

  /** 获取composer prompt */
  async getComposerPrompt(params: ComposerPromptBody) {
    const { data } = await this.post<string>(
      "/eapi/kwaipilot/plugin/composer/prompt",
      JSON.stringify(params),
    );
    return data;
  }

  async getCustomPrompts(): Promise<CustomPromptData[]> {
    const username = await this.getUsername();
    const { data } = await this.get<{ prompts: CustomPromptData[] }>(
      `/eapi/kwaipilot/prompt_management/get_prompt?username=${username ?? ""}`,
    );
    return data?.prompts || [];
  }

  fetchEventSource(url: string, options: FetchEventSourceInit): Promise<void> {
    return this.rawFetchEventSource(url, options);
  }
}

export const httpClient = new Api();
