import { RelateCode } from "@/components/RelateCode";
import { RemarkGfm } from "@kwaipilot/markdown-render";
import { useCallback, useMemo } from "react";
import { wrapHttpLinks } from "@/utils/utils";
import { useColorMode } from "@chakra-ui/react";

import clsx from "clsx";
import { useRecordStore } from "@/store/record";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { ICachedMessageAnswer, QAItem } from "@shared/types/chatHistory";
import ReactMarkdown, { Components } from "react-markdown";
import { MarkdownCodeRenderer } from "./MarkdownCodeRender";
import { KwaipilotArtifact } from "./KwaipilotArtifact";
import { rehypePlugins } from "@/utils/markdown";
import TerminalIcon from "@/assets/terminal.svg?react";
import { SingleIcon } from "@/components/SingleIcon";
import { ReportOpt } from "@shared/types/logger";
import { collectClick, reportUserAction } from "@/utils/weblogger";
import CopyIcon from "@/assets/copy.svg?react";
import { Icon } from "@/components/Union/t-iconify";
import { getFileExtension, getIcon } from "@/utils/fileIcon";
import { Highlight } from "@/components/Dialog/Highlight";
import { MarkdownRenderContext } from "./MarkdownRenderContext";

const TerminalBlock = ({
  match,
  iconType,
  children,
  onCopy,
  onExecute,
}: {
  match: RegExpExecArray;
  iconType: string;
  children: React.ReactNode;
  onCopy: (text: string) => void;
  onExecute: (text: string) => void;
}) => {
  return (
    <div className="overflow-x-hidden border border-[var(--vscode-terminal-border)] rounded my-[0] code-container">
      <div
        className="flex items-center gap-2 text-[13px] leading-[18px] h-[32px] bg-statusBarItem-remoteHoverBackground px-[12px] rounded-t-[4px]"
      >
        <div className="flex items-center gap-[4px] justify-center">
          <Icon icon={iconType} className="w-[14px] h-[14px]" />
          <span>{match[1]}</span>
        </div>
        <div className="flex items-center ml-auto text-foreground">
          <SingleIcon
            title="复制"
            onClick={() => onCopy(String(children).replace(/\n$/, ""))}
          >
            <CopyIcon />
          </SingleIcon>
          <SingleIcon
            title="插入至终端中"
            onClick={() => onExecute(String(children).replace(/\n$/, ""))}
          >
            <TerminalIcon />
          </SingleIcon>
        </div>
      </div>
      <Highlight language="bash">
        {children}
      </Highlight>
    </div>
  );
};

// 预处理Markdown文本，处理代码块中的语言标识符中的空格问题
const preprocessMarkdown = (markdown: string): string => {
  // 匹配代码块的开始行，使用[ ]只匹配空格
  // 例如: ```html:abc- aaa
  const codeBlockRegex = /^```([\w:.]+?)[ ]+(.+?)$/gm;

  return markdown.replace(codeBlockRegex, (match, languagePart, remainingPart) => {
    // 如果languagePart包含冒号，说明可能有路径部分
    if (languagePart.includes(":")) {
      // 保留原始的格式，只是将空格转换为特殊标记
      return "```" + languagePart + "-SPACE-" + remainingPart;
    }
    return match;
  });
};

export const MarkdownRender = ({
  answer: detail,
  qaItem,
}: {
  answer: ICachedMessageAnswer;
  qaItem: QAItem;
}) => {
  const { colorMode: theme } = useColorMode();

  const parsedAnswerReply = useRecordStore(s => s.parsedAnswerReply);

  const detailContent = useMemo(
    () => (detail.isSelf
      ? detail.question
      : detail.answerId && parsedAnswerReply[detail.answerId]
        ? parsedAnswerReply[detail.answerId]
        : detail.reply || ""
    ),
    [detail.answerId, detail.isSelf, detail.question, detail.reply, parsedAnswerReply],
  );
  const formatContent = useMemo(() => {
    const finalContent = wrapHttpLinks(detailContent);
    const output = finalContent.replace(/(\$\$)/g, function (match) {
      return "\n" + match + "\n";
    });
    if (output === "#") return "\\#";
    // 预处理Markdown文本
    return preprocessMarkdown(output);
  }, [detailContent]);

  const insertTerminalCommand = useCallback(
    (text: string) => {
      // 通过 bridge 调用插件侧的 API，在 VSCode 中新建终端并插入命令
      const command = text.replace(/\n$/, "");

      // 埋点统计
      const parms: ReportOpt<"codeBlockApply"> = {
        key: "codeBlockApply",
        type: "llmMsgCode",
      };
      reportUserAction(parms, detail.id);
      collectClick("VS_TERMINAL_EXECUTE");

      kwaiPilotBridgeAPI.terminal
        .sendText(command)
        .then((response) => {
          if (!response.success) {
            kwaiPilotBridgeAPI.showToast({
              message: `执行终端命令失败: ${response.message || "未知错误"}`,
              level: "error",
            });
          }
          else {
            kwaiPilotBridgeAPI.showToast({
              message: command.includes("\n")
                ? "多行命令已插入终端，请按需执行"
                : "命令已发送到终端",
              level: "info",
            });
          }
        })
        .catch(() => {
          kwaiPilotBridgeAPI.showToast({
            message: "执行终端命令失败，请检查终端状态",
            level: "error",
          });
        });
    },
    [detail.id],
  );

  const markdownComponents = useMemo<Components>(() => ({
    code: (props) => {
      const { className, children } = props;
      const match = /language-(\w+)/.exec(className || "");
      const TERMINAL_LANGUAGES = ["bash", "shell", "sh", "zsh", "cmd"];
      const isTerminalCommand = match && TERMINAL_LANGUAGES.includes(match[1]);
      const iconType = getIcon("index" + getFileExtension(match?.[1] || "typescript"), false);

      if (isTerminalCommand) {
        return (
          <TerminalBlock
            match={match}
            iconType={iconType}
            onCopy={(text) => {
              kwaiPilotBridgeAPI.copyToClipboard(text);
              const parms: ReportOpt<"codeBlockApply"> = {
                key: "codeBlockApply",
                type: "llmMsgCode",
              };
              reportUserAction(parms, detail.id);
              collectClick("VS_COPY_CODE");
            }}
            onExecute={insertTerminalCommand}
          >
            {children}
          </TerminalBlock>
        );
      }

      return <MarkdownCodeRenderer {...props} />;
    },
    // pre: ({ children }) => (
    //   <pre className="blog-pre">{children}</pre>
    // ),
    div: ({ className, node, ...props }) => {
      if (className?.includes("__KwaipilotArtifact__")) {
        const messageId = node?.properties?.dataMessageId as string;

        const artifactKey = messageId;

        return <KwaipilotArtifact key={artifactKey} messageId={messageId} />;
      }
      /** 渲染指令块 */
      return <div {...props} className={clsx(className || "")}></div>;
    },
    h1(props) {
      return (
        <h1
          {...props}
          className="leading-[22px] pt-[9px] pb-[7px]"
        >
        </h1>
      );
    },
    h2(props) {
      return (
        <h2
          {...props}
          className="leading-[22px] pt-[8px] pb-[6px]"
        >
        </h2>
      );
    },
    h3(props) {
      return (
        <h3
          {...props}
          className="leading-[22px] pt-[7px] pb-[5px]"
        >
        </h3>
      );
    },
    p(props) {
      return (
        <p
          {...props}
          className="leading-[22px] pt-[3px] pb-[3px]"
        >
        </p>
      );
    },
  }), [detail.id, insertTerminalCommand]);

  return (
    <MarkdownRenderContext.Provider value={{ qaItem, detail }}>
      <div className={` width-[100%] overflow-x-hidden text-[var(--custom-text-common)]`}>

        {detail.codeSearchList && (
          <RelateCode list={detail.codeSearchList}></RelateCode>
        )}
        <ReactMarkdown
          remarkPlugins={[RemarkGfm]}
          rehypePlugins={rehypePlugins(!detail.isSelf) as any}
          children={formatContent}
          components={markdownComponents}
        />

        {!!detail.actionResults?.length && (
          <div className={`mt-[8px] link-text-${theme}`}>
            <div>
              找到了
              {detail.actionResults?.length}
              篇文档作为参考：
            </div>
            {detail.actionResults?.map((item, index) => {
              return (
                <div key={index}>
                  <a
                    target="_blank"
                    href={item.link}
                    rel="noreferrer"
                    className="underline"
                  >
                    {item.title}
                  </a>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </MarkdownRenderContext.Provider>
  );
};
