import { FileCard } from "@/components/FileCard";
import { Code } from "@/components/Code";
import { useColorMode, useToast } from "@chakra-ui/react";
import { ICachedMessageQuestion, ICachedMessageQuestionV1, ICachedMessageQuestionV2 } from "@shared/types/chatHistory";
import { RichEditor } from "@/logics/UserInputTextarea/RichEditor";
import { useCallback, useMemo, useRef } from "react";
import { LexicalEditor } from "lexical";
import { useBridgeObservableAPI } from "@/bridge/useBridgeObservableAPI";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { VsCodeToast } from "@/components/ui/VsCodeToast";
import { ContextHeaderReadonly } from "@/logics/UserInputTextarea/ContextHeader";
import { UserInputTextAreaContext } from "@/logics/UserInputTextarea/UserInputTextAreaContext";

const UserMessageV1 = ({ data }: { data: ICachedMessageQuestionV1 }) => {
  const { question } = data;
  const { colorMode: theme } = useColorMode();

  // 定义正则表达式模式字符串
  const regexPattern
    = /(<div className="kwaipilot-command-block">(.*?)<\/div>)/;

  // 调用replace方法，分割捕获的部分并进行替换
  let pre = "";
  const parts = question.split(regexPattern).map((part, index) => {
    // 为每个part创建新的正则表达式实例
    const regex = new RegExp(regexPattern);
    const match = regex.exec(part);

    if (match) {
      pre = match[2];
      return (
        <div
          key={index}
          className={`kwaipilot-command-block text-[13px] leading-[16px] h-[22px] inline-block rounded px-[4px] py-[3px] whitespace-pre-wrap ${
            theme === "dark"
              ? "bg-[rgba(255,255,255,0.16)]"
              : "bg-[rgba(239,247,253,1)]"
          } text-text-common-secondary`}
        >
          {match[2]}
        </div>
      );
    }
    else {
      if (pre === part) {
        pre = "";
        return pre;
      }
      else {
        return part;
      }
    }
  });

  return (
    <div className="flex justify-end ml-[24px]">
      <div className="bg-list-inactiveSelectionBackground rounded-l-[8px] rounded-tr-[2px] rounded-br-[8px] p-[12px] max-w-full">
        <div className="text-[13px] font-normal leading-[20px] whitespace-pre-wrap">
          {parts}
        </div>
        {data.fileList?.length
          ? (
              <div className="flex flex-wrap py-[6px] gap-[10px] rounded-none">
                {data.fileList.map(file => (
                  <FileCard
                    file={file}
                    key={file.uid}
                    className="w-[calc(50%-5px)] only:min-w-[200px]"
                  />
                ))}
              </div>
            )
          : null}
        {data.code && (
          <Code
            filename={data.filename || ""}
            startLine={data.startLine || 0}
            endLine={data.endLine || 0}
            content={data.code}
            path={data.filename || ""}
          />
        )}
      </div>
    </div>
  );
};

const UserMessageV2 = ({ data }: { data: ICachedMessageQuestionV2 }) => {
  const { editorState } = data;
  const panelRef = useRef<HTMLDivElement>(null);
  const editorRef = useRef<LexicalEditor>(null);
  const initialEditorState = useMemo(() => editorState ? JSON.stringify(editorState) : undefined, [editorState]);

  return (
    <div className="flex justify-end ml-[24px]">
      <div className="bg-list-inactiveSelectionBackground rounded-l-[8px] rounded-tr-[2px] rounded-br-[8px] p-[12px] max-w-full">
        <div ref={panelRef}></div>
        {data.contextItems?.length ? <ContextHeaderReadonly pb={3} persistentNodes={data.contextItems || []} /> : null}
        <UserInputTextAreaContext.Provider value={{ role: "conversation" }}>
          <RichEditor
            editable={false}
            editorRef={editorRef}
            onSubmit={async () => {}}
            changeEditorState={() => {}}
            disabled={false}
            editorClassName=" w-fit p-0"
            className=""
            placeholder={<div></div>}
            mode="chat"
            initialEditorState={initialEditorState}
            customOptions={{
              sharpCommandEnabled: false,
            }}
          />
        </UserInputTextAreaContext.Provider>
      </div>
    </div>
  );
};

export const UserMessage = ({ data }: { data: ICachedMessageQuestion }) => {
  const isDeveloperMode = useBridgeObservableAPI("isDeveloperMode");

  const toast = useToast({
    render: VsCodeToast,
  });

  const copySessionId = useCallback(() => {
    kwaiPilotBridgeAPI.copyToClipboard(data.id);
    toast({
      title: "复制成功",
      description: `Id已复制到剪贴板 ${data.id}`,
    });
  }, [data.id, toast]);

  const debugInfo = isDeveloperMode
    ? (
        (
          <div>
            Id:
            <span onClick={copySessionId}>
              {data.id}
            </span>
          </div>
        )
      )
    : undefined;
  if ("v2" in data && data.v2) {
    return (
      <div>
        <UserMessageV2 data={data} />
        {debugInfo}
      </div>
    );
  }
  return <UserMessageV1 data={data} />;
};
