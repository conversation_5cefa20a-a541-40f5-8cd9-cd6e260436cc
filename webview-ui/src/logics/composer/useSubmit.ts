import { kwai<PERSON>ilotBridgeAPI } from "@/bridge";
import { MentionNode } from "@/components/TextArea/lexical/CommandNode";
import {
  isMentionNode,
  isSharpCommandMentionNode,
  SerializedMentionNode,
} from "shared/lib/MentionNode";
import { transformToPlainTextForHumanReading } from "@/components/TextArea/lexical/editorState";
import { generateCustomUUID } from "@/utils/sessionUtils";
import { collectClick, reportUserAction } from "@/utils/weblogger";
import { CommandType, SharpCommand } from "@shared/types";
import {
  $createParagraphNode,
  $getRoot,
  SerializedEditorState,
} from "lexical";
import { useCallback, useMemo } from "react";
import {
  isCheckpointCreatedMessage,
  isTerminalMessage,
  isToolEditFileMessage,
} from "shared/lib/agent/isToolMessage";
import { useRestoreAndSendDialog } from "./components/HumanMessage/RestoreAndSendDialog";
import { useSendConfirmDialog } from "./components/HumanMessage/SendConfirmDialog";
import { useUserStore } from "@/store/user";
import { useAsync } from "react-use";
import { useComposerState } from "./context/ComposerStateContext";
import {
  findFirst,
  traversePreOrder,
} from "@/components/TextArea/lexical/traversal";
import { fetchSummaryConversation } from "@/http/api/summaryConversation";
import { ReportOpt } from "@shared/types/logger";
import { chatId } from "@/utils/chatId";
import {
  MentionNodeV2Structure,
} from "shared/lib/MentionNodeV2/nodes";
import {
  ContextHeaderItem,
} from "../UserInputTextarea/ContextHeader/ContextHeaderContext";
import { DOM } from "@/utils/dom";
import { UserInputTextareaProps } from "../UserInputTextarea/UserInputTextArea";
import { collectMentionNodeV2 } from "../UserInputTextarea/ContextHeader/collectMentionNode";

export function useSubmit({
  role,
}: {
  role: "bottom" | "conversation";
}) {
  const {
    editingMessageTs,
    localMessages,
    isCurrentWorkspaceSession,
    sessionId,
  } = useComposerState();

  const userInfo = useUserStore(state => state.userInfo);

  const { invoke: invokeRestoreAndSendDialog } = useRestoreAndSendDialog();
  const { invoke: invokeSendConfirmDialog } = useSendConfirmDialog();

  const relatedCheckpointMessage = useMemo(() => {
    if (!editingMessageTs) {
      return null;
    }
    const humanMessageI = localMessages.findIndex(
      v => v.ts === editingMessageTs,
    );
    if (humanMessageI === -1) {
      return null;
    }
    const prediction = localMessages[humanMessageI + 1];
    if (prediction && isCheckpointCreatedMessage(prediction)) {
      return prediction;
    }
    return null;
  }, [editingMessageTs, localMessages]);

  const { value: workspaceFileUri } = useAsync(
    () => kwaiPilotBridgeAPI.extensionComposer.$getWorkspaceFile(),
    [],
  );
  const isVscodeWorkspace = useMemo(() => {
    return Boolean(workspaceFileUri);
  }, [workspaceFileUri]);

  const shouldShowRestoreDialogBeforeSubmit = useMemo(() => {
    if (isVscodeWorkspace) {
      /* vscode 不支持回退 */
      return false;
    }
    if (role === "bottom") {
      // 现在没有重做功能了
      return false;
    }
    if (!editingMessageTs) {
      return false;
    }
    if (!isCurrentWorkspaceSession) {
      return false;
    }

    const humanMessageI = localMessages.findIndex(
      v => v.ts === editingMessageTs,
    );
    if (humanMessageI === -1) {
      return false;
    }
    const toBeRestoredMessages = localMessages.slice(humanMessageI + 1);
    const haveEditMessage = toBeRestoredMessages.some(
      v => isToolEditFileMessage(v) || isTerminalMessage(v),
    );
    return Boolean(relatedCheckpointMessage) && haveEditMessage;
  }, [
    editingMessageTs,
    isCurrentWorkspaceSession,
    isVscodeWorkspace,
    localMessages,
    relatedCheckpointMessage,
    role,
  ]);

  const onSubmit = useCallback<(...args: Parameters<UserInputTextareaProps["doSubmit"]>) => Promise<boolean>>(async ({
    contextHeaderState,
    editor,
  }) => {
    collectClick("VS_SUBMIT_BUTTON");
    if (!editor) {
      return false;
    }

    const editorState = editor.getEditorState().toJSON();
    if (!editorState || !editor) {
      kwaiPilotBridgeAPI.showToast({
        level: "error",
        message: "编辑器未初始化, 请重试",
      });
      return false;
    }
    if (!userInfo) {
      return false;
    }
    const uniqueId
      = Date.now().toString(36) + Math.random().toString(36).substr(2);
    chatId.updateChatId(uniqueId);

    const slashCommand = findFirst(
      editorState,
      (node): node is SerializedMentionNode =>
        isMentionNode(node) && node.commandType === CommandType.SLASH,
    );

    const questionForHumanReading
      = transformToPlainTextForHumanReading(editorState);

    if (slashCommand) {
      // 如果 editorState 中有 slash 命令，则返回一个简化的 editorState
      editor.update(
        () => {
          const root = $getRoot();
          root.clear();
          const p = $createParagraphNode();
          root.append(p);
          const commandNode = MentionNode.importJSON(slashCommand);
          console.log(commandNode);
        },
        { discrete: true },
      );
    }

    const promptRules = collectMentionNodeV2(editor)
      .map(v => v.__structure)
      .filter(v => v.type === "rule")
      .map(v => v.relativePath);
    const contextRules = contextHeaderState.nodes
      .filter(node => node.structure.type === "rule")
      .map(node => node.structure.relativePath);
    const rules = [...new Set([...promptRules, ...contextRules])];

    const targetSessionId
      = sessionId || localMessages[0]?.sessionId || generateCustomUUID();

    let title = "";
    fetchSummaryConversation(questionForHumanReading, (chunk) => {
      title += chunk;
      kwaiPilotBridgeAPI.updateComposerSessionName({
        sessionId: targetSessionId,
        name: title,
      });
    });

    // 立即滚动到底部
    const dialogContainer = DOM.$(".chat-dialog-container");
    if (dialogContainer) {
      dialogContainer.scrollTo({
        top: 0,
        behavior: "instant",
      });
    }

    const contextLog = collectContextLog(editorState, contextHeaderState.nodes);
    if (contextLog) {
      const param: ReportOpt<"input_context_send"> = {
        key: "input_context_send",
        type: contextLog,
        content: "new_composer",
      };
      reportUserAction(param);
    }

    const conversationId = generateCustomUUID();

    kwaiPilotBridgeAPI.extensionComposer.$postMessageToComposerEngine({
      type: "newTask",
      // 这个字段其实已经没用了
      task: questionForHumanReading,
      reqData: {
        sessionId: targetSessionId,
        chatId: conversationId,
      },
      rules,
      editorState: editor.getEditorState().toJSON(),
      questionForHumanReading: questionForHumanReading,
      // TODO: context info是否拆开
      contextItems: contextHeaderState.nodes.map(node => node.structure),
      editingMessageTs,
    });

    return true;

    // 发送给extension
  }, [userInfo, sessionId, localMessages, editingMessageTs]);

  const onSubmitPrecheck = useCallback(async (): Promise<boolean> => {
    if (shouldShowRestoreDialogBeforeSubmit) {
      const result = await invokeRestoreAndSendDialog();
      if (result === "restoreAndSend") {
        if (!relatedCheckpointMessage?.lastCheckpointHash) {
          kwaiPilotBridgeAPI.showToast({
            message: "没有找到相关的检查点",
            level: "error",
          });
          return false;
        }
        if (!editingMessageTs) {
          return false;
        }
        await kwaiPilotBridgeAPI.extensionComposer.$restoreCheckpoint({
          humanMessageTs: editingMessageTs,
          restoreCommitHash: relatedCheckpointMessage.lastCheckpointHash,
          updateStateImmediately: false,
        });
        return true;
      }
      else if (result === "keepAndSend") {
        if (!editingMessageTs) {
          return false;
        }
        await kwaiPilotBridgeAPI.extensionComposer.$revertHistory({
          humanMessageTs: editingMessageTs,
          updateStateImmediately: false,
        });
        return true;
      }
    }
    else if (editingMessageTs) {
      const result = await invokeSendConfirmDialog();
      if (result === "confirm") {
        await kwaiPilotBridgeAPI.extensionComposer.$revertHistory({
          humanMessageTs: editingMessageTs,
          updateStateImmediately: false,
        });
        return true;
      }
    }
    else {
      return true;
    }
    return false;
  }, [shouldShowRestoreDialogBeforeSubmit, editingMessageTs, invokeRestoreAndSendDialog, relatedCheckpointMessage?.lastCheckpointHash, invokeSendConfirmDialog]);

  const doSubmit = useCallback<UserInputTextareaProps["doSubmit"]>(async (state) => {
    const preCheckResult = await onSubmitPrecheck();
    if (!preCheckResult) {
      return { result: false };
    }
    const res = await onSubmit(state);
    return { result: res };
  }, [onSubmitPrecheck, onSubmit]);

  return {
    doSubmit,
  };
}

/**
 * 从用户输入中获取其使用 #指令的记录
 *
 * 包括 旧版的 sharp command(兼容普通对话) 和新版的 mention context
 */
export function collectContextLog(
  editorState: SerializedEditorState,
  contextItems: ContextHeaderItem[],
): string {
  const commands = new Set<string>();
  const LEGACY_SHARP_COMMAND_LOG_MAP: Record<SharpCommand, string> = {
    [SharpCommand.CURRENT_FILE]: "currentFile",
    [SharpCommand.FILE]: "file",
    [SharpCommand.FOLDER]: "dir",
    [SharpCommand.CODEBASE]: "codebase",
    [SharpCommand.RULES]: "rule",
  };
  const CONTEXT_TYPE_MAP: Record<MentionNodeV2Structure["type"], string> = {
    file: "file",
    remoteFile: "remoteFile",
    rule: "rule",
    tree: "dir",
    selection: "selection",
    web: "web",
    codebase: "codebase",
    knowledge: "knowledge",
    slashCommand: "slashCommand",
  };
  for (const node of traversePreOrder(editorState.root)) {
    if (isMentionNode(node) && isSharpCommandMentionNode(node)) {
      commands.add(LEGACY_SHARP_COMMAND_LOG_MAP[node.key as SharpCommand]);
    }
  }
  for (const item of contextItems) {
    if (item.followActiveEditor) {
      commands.add("currentFile");
    }
    else {
      commands.add(CONTEXT_TYPE_MAP[item.structure.type]);
    }
  }
  return Array.from(commands).join(",");
}
