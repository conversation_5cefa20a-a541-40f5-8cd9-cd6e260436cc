export type LoggerSupplementaryField = {
  /** 捕获到的具体报错 */
  err?: unknown;
  /** 可以时Error.message,也可以是明确的自定义信息 */
  reason?: string;
  /** 日志目标数据 */
  value?: any;

};

export type ReportKeys = {
  chat: "new-chat" | "old-chat" | "inline-chat";
  themeChange: undefined;
  uploadFile: undefined;
  chSetting: "modelType" | "webSearch" | "knRepo";
  showRunDetail: "running" | "end";
  closeRunDetail: undefined;
  guessAsk: string;
  copy: "userMsg" | "llmMsg" | "llmMsgCode";
  reNew: undefined;
  like: undefined;
  dislike: undefined;
  goBottom: undefined;
  source: "newWindow" | "currentPage" | "newWindow-drawer";
  preview: "userMsg" | "llmMsg" | "source";
  img: undefined;
  windowDrag: "pdf" | "web";
  nextQuestion: string;
  pdfZoom: "slider" | "select";
  contentHeight: undefined;
  exampleUse: string;
  windowSize: "pdf" | "web";
  share: undefined;
  inlay_hints:
    | "单元测试"
    | "函数注释"
    | "行间注释"
    | "代码优化"
    | "函数拆分"
    | "代码解释";
  shortcutInstruction: string;
  codeSearch: string;
  inline_chat: "close" | "pause" | "window_show";
  codeBlockApply: "llmMsgCode";
  instantApply: undefined;
  instant_apply_feedback: "accept" | "reject";
  coding: undefined;
  feature_reco: "一键优化代码" | "永久关闭" | "曝光";
  sidebar_show: "ext_click" | "code_action" | "menu_click" | "key_binding" | "unknown";
  compose_agent_show: undefined;
  agent_code_keep: "all" | "single";
  agent_code_undo: "all" | "single";
  composerNewTask: "composerNewTask";
  rule_setting: "user_rule" | "project_rule";
  rules_page_show: undefined;
  mcp_page_show: undefined;
  file_index_page_show: undefined;
  basics_page_show: undefined;
  function_page_show: undefined;
  code_storage: undefined;
  mcp_action: "mcp_setting_refresh" | "mcp_setting_editmcp" | "mcp_chat_click";
  mcp_jump: "mcp_setting_jsonconf" | "mcp_setting_hub";
  mcp_user_conf: "mcp_user_conf";
  mcp_user_confopen: "mcp_user_confopen";
  input_add_context: "textarea" | "context" | "shortcut" | "clipboard" | "default";
  input_context_send: string;
  input_rules_create: undefined;
  agent_linter_fix_show: undefined;
  agent_linter_fix_click: "fix_click";
  accept_line_shortcut: undefined;
};

export interface ReportOpt<T extends keyof ReportKeys> {
  key: T;
  type: ReportKeys[T];
  content?: string;
  subType?: string;
  applyId?: string;
}

export interface LoggerCustomEventStructure<T extends keyof ReportKeys = keyof ReportKeys> {
  conversationId: string;
  time: number;
  device: "kwaipilot-vscode" | string;
  operator: string;
  type: ReportKeys[T];
  content: string;
  subType: string;
  chatId: string;
  applyId: string;
  pluginVersion: string;
  ideVersion: string;
  platform: string;
  release: string;
  arch: string;
  machine: string;
  hostname: string;
  /* 当前是否在 vscode workspace 中, undefined 表示未确定,及其他任何异常情况,可以把 undefined 的数据清洗掉 */
  isInWorkspace: boolean | undefined;
}
