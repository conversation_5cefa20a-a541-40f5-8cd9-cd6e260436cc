export interface UploadFile {
  biz?: string;
  filename: string;
  id?: number;
  uid: string;
  path?: string;
  url?: string;
  type?: string;
  size: number;
  username?: string;
  progress?: number;
  status?: "uploading" | "done" | "error";
  [key: string]: any;
}

export interface BridgeUploadFile {
  uri: string;
  relativePath: string;
  uploadInfo: UploadFile;
}

export interface ReadDirectoryResult {
  files: string[];
}
