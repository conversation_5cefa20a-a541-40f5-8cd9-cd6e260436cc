import { convertThemeToVSCodeOwnTheme, Theme } from "shared";
import { ServiceModule } from "..";
import * as vscode from "vscode";
import path from "path";
import fs from "fs";
import { ContextManager } from "../../base/context-manager";
import { Bridge } from "@bridge";
import { LoggerManager } from "../../base/logger";
import { NATIVE_BRIDGE_EVENT_NAME } from "shared/lib/bridge";
import { safeJsonParse } from "../../utils/safeJsonParse";

/**
 * 加载主题配置，处理 include 关系 参考 vscode  https://github.com/microsoft/vscode/blob/main/src/vs/workbench/services/themes/common/colorThemeData.ts#L711
 * @param themeContent 主题的 JSON 内容
 * @param basePath 主题文件的基础路径（用于解析 include 的相对路径）
 * @param fs 文件系统对象，用于读取 include 的文件
 * @param result 累积的结果对象（递归调用时使用）
 * @returns 处理后的完整主题配置
 */
function loadThemeFromContent(
  themeContent: string,
  basePath: string,
  result: {
    name: string;
    tokenColors: any[];
    colors: Record<string, any>;
    semanticTokenRules: any[];
    semanticHighlighting: boolean;
  } = {
    name: "",
    tokenColors: [],
    colors: {},
    semanticTokenRules: [],
    semanticHighlighting: false,
  },
): typeof result {
  try {
    // 解析 JSON 内容
    const contentValue = safeJsonParse(themeContent);

    // 处理 include
    if (contentValue.include) {
      // 构建被包含文件的完整路径

      const includePath = path.resolve(basePath, contentValue.include);
      // 读取被包含的文件内容
      const includeContent = fs.readFileSync(includePath).toString();
      // 递归处理被包含的文件
      loadThemeFromContent(includeContent, path.dirname(includePath), result);
    }

    // 处理颜色设置
    if (contentValue.colors) {
      if (typeof contentValue.colors !== "object") {
        throw new Error(`颜色主题配置错误：'colors' 属性不是对象类型`);
      }

      // 合并颜色配置
      for (const colorId in contentValue.colors) {
        const colorVal = contentValue.colors[colorId];
        if (colorVal === undefined) {
          // 忽略设置为 undefined 的颜色
          delete result.colors[colorId];
        }
        else if (typeof colorVal === "string") {
          result.colors[colorId] = colorVal; // 在实际应用中可能需要将颜色字符串转换为颜色对象
        }
      }
    }

    // 处理 tokenColors
    const tokenColors = contentValue.tokenColors;
    if (tokenColors) {
      if (Array.isArray(tokenColors)) {
        result.tokenColors.push(...tokenColors);
      }
    }

    // 处理语义标记颜色
    const semanticTokenColors = contentValue.semanticTokenColors;
    if (semanticTokenColors && typeof semanticTokenColors === "object") {
      // 在实际应用中，这里需要处理语义标记的规则
      // 这个部分取决于你的具体需求
    }

    // 设置语义高亮选项
    result.semanticHighlighting
      = result.semanticHighlighting || contentValue.semanticHighlighting;

    return result;
  }
  catch (e) {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    console.log("加载主题设置错误", e?.message);
    return result;
  }
}

// 提取重复逻辑到单独的函数
function getThemeById(themeId: string): Theme | undefined {
  const extensions = vscode.extensions.all;
  const themeExtensions = extensions.filter(
    i => i.packageJSON?.contributes?.themes?.length,
  );

  for (let i = 0; i < themeExtensions.length; i++) {
    const extensionPath = themeExtensions[i].extensionPath;
    const themes = themeExtensions[i].packageJSON.contributes.themes;
    for (let j = 0; j < themes.length; j++) {
      const theme = themes[j];
      const id = theme.id || theme.label;
      if (id === themeId) {
        const themePath = path.resolve(extensionPath, theme.path);
        const themeSettings = fs.readFileSync(themePath, "utf-8");
        const parseThemeSettings = loadThemeFromContent(
          themeSettings,
          path.dirname(themePath),
        );
        const name = parseThemeSettings.name || theme.label;
        if (!parseThemeSettings.name) {
          parseThemeSettings.name = name;
        }
        return {
          id: themeId,
          settings: parseThemeSettings,
          name: name,
        };
      }
    }
  }
  return undefined;
}

export function getThemeInitConfig(): Theme | undefined {
  try {
    const extensions = vscode.extensions.all;
    const colorThemeId = vscode.workspace
      .getConfiguration("workbench")
      .get("colorTheme") as string;

    const activeTheme = vscode.window.activeColorTheme;
    const colorThemeKind = activeTheme.kind;
    // @IMP: 注意：如果没有适配的主题名称，会返回默认主题名称
    const actualThemeId = convertThemeToVSCodeOwnTheme(colorThemeId, colorThemeKind);
    console.log("当前主题", colorThemeId, colorThemeKind, actualThemeId);
    const themeExtensions = extensions.filter(
      i => i.packageJSON?.contributes?.themes?.length,
    );
    console.log("当前主题 插件", themeExtensions);

    const themes: Record<string, Theme> = {};

    themeExtensions.forEach((i) => {
      const extensionPath = i.extensionPath;
      i.packageJSON.contributes.themes.forEach(
        (theme: { path: string; id: string; label: string }) => {
          const themePath = path.resolve(extensionPath, theme.path);
          const themeSettings = fs.readFileSync(themePath, "utf-8");
          const parseThemeSettings = loadThemeFromContent(
            themeSettings,
            path.dirname(themePath),
          );
          const themeId = theme.id || theme.label;

          const name = parseThemeSettings.name || theme.label;

          if (!parseThemeSettings.name) {
            parseThemeSettings.name = name;
          }
          themes[themeId] = {
            id: themeId,
            settings: parseThemeSettings,
            name: name,
          };
        },
      );
    });
    console.log("当前主题 themes", themes);
    return themes[actualThemeId];
  }
  catch (e) {
    // nothing
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    console.log("解析主题错误", e?.message);
  }
}

export class ThemeService extends ServiceModule {
  private themes: Record<string, Theme> = {};

  constructor(ext: ContextManager) {
    super(ext);
    this.registerListener();
  }

  registerListener() {
    this.bridge.registerHandler(
      NATIVE_BRIDGE_EVENT_NAME.GET_THEME_SETTINGS,
      (data) => {
        if (!data?.themeId) {
          throw new Error("payload is required");
        }
        // @IMP: 注意：如果没有适配的主题名称，会返回默认主题名称
        const themeId = convertThemeToVSCodeOwnTheme(data.themeId, data.themeKind);
        const theme = this.themes[themeId];

        if (theme) {
          return theme;
        }

        const newTheme = getThemeById(themeId);
        if (newTheme) {
          this.themes[themeId] = newTheme;
          return newTheme;
        }
      },
    );
  }

  private get bridge() {
    return this.getBase(Bridge);
  }

  private get logger() {
    return this.getBase(LoggerManager);
  }
}
